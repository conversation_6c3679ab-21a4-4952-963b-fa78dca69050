# 商品服务配置
quarkus:
  application:
    name: product-service
    version: 1.0.0

  # HTTP配置
  http:
    port: 8082
    host: 0.0.0.0
    cors:
      ~: true
      origins: "*"
      methods: "*"
      headers: "*"

  # 数据源配置 - PostgreSQL响应式
  datasource:
    reactive:
      url: ${DB_URL:vertx-reactive:postgresql://*************:35432/visthink_product}
      max-size: 20
      username: ${DB_USERNAME:postgres}
      password: ${DB_PASSWORD:zylp}
      idle-timeout: PT10M
      max-lifetime: PT30M

  # 日志配置
  log:
    level: INFO
    category:
      "com.visthink": DEBUG
      "org.hibernate.SQL": ${LOG_SQL:false}
      "org.hibernate.type.descriptor.sql.BasicBinder": ${LOG_SQL:false}
    console:
      enable: true
      format: "%d{yyyy-MM-dd HH:mm:ss,SSS} %-5p [%c{3.}] (%t) %s%e%n"
      color: true

  # OpenAPI文档配置
  smallrye-openapi:
    info-title: 商品服务API
    info-version: 1.0.0
    info-description: Visthink ERP商品管理微服务API文档
    info-contact-name: Visthink Team
    info-contact-email: <EMAIL>
    servers:
      - url: http://localhost:8082
        description: 开发环境
      - url: http://localhost/api/product
        description: 网关环境

  # 健康检查配置
  smallrye-health:
    root-path: /health

  # 指标配置
  micrometer:
    binder:
      jvm: true
      system: true
      http-server: true
    export:
      prometheus:
        enabled: true
        path: /metrics

  # Redis配置
  redis:
    hosts: ${REDIS_URL:redis://127.0.0.1:6379}
    password: ${REDIS_PASSWORD:zylp}
    database: 1
    timeout: 10s
    max-pool-size: 20
    max-pool-waiting: 30
    devservices:
      enabled: false

  # Consul配置
  consul-config:
    enabled: ${CONSUL_ENABLED:true}
    agent-host-port: ${CONSUL_HOST:localhost:8500}
    key-prefix: config/product-service
    fail-on-missing-key: false
    config-properties:
      - key: datasource
        optional: true
      - key: redis
        optional: true

# 业务配置
app:
  # 租户配置
  tenant:
    enabled: true
    header-name: X-Tenant-Id
    default-tenant: 1

  # 缓存配置
  cache:
    product:
      ttl: 300 # 5分钟
      max-size: 1000
    sku:
      ttl: 300
      max-size: 2000

  # 分页配置
  page:
    default-size: 20
    max-size: 100

  # 文件上传配置
  upload:
    max-file-size: 10MB
    allowed-types: jpg,jpeg,png,gif,webp
    base-url: ${FILE_BASE_URL:http://localhost:9000}

  # 平台集成配置
  platform:
    enabled: true
    sync-interval: 300 # 5分钟同步一次
    batch-size: 100
    timeout: 30s

# 环境特定配置
"%dev":
  quarkus:
    log:
      category:
        "com.visthink": DEBUG
    datasource:
      reactive:
        url: vertx-reactive:postgresql://*************:35432/visthink_product
    redis:
      hosts: redis://127.0.0.1:6379

"%test":
  quarkus:
    datasource:
      reactive:
        url: vertx-reactive:h2:mem:test;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE

"%prod":
  quarkus:
    log:
      level: INFO
      category:
        "com.visthink": INFO
    datasource:
      reactive:
        url: ${DB_URL}
    redis:
      hosts: ${REDIS_HOSTS}
    consul-config:
      enabled: true
      agent-host-port: ${CONSUL_HOST}

# 服务注册配置
consul:
  service:
    name: product-service
    port: 8082
    health-check-path: /health/ready
    health-check-interval: 10s
    tags:
      - microservice
      - product
      - quarkus
