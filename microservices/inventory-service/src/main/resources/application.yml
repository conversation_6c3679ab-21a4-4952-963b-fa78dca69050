# 库存服务配置
quarkus:
  application:
    name: inventory-service
    version: 1.0.0

  # HTTP配置
  http:
    port: 8083
    host: 0.0.0.0
    cors:
      ~: true
      origins: "*"
      methods: "*"
      headers: "*"

  # 数据源配置
  datasource:
    db-kind: postgresql
    username: ${DB_USERNAME:user}
    password: ${DB_PASSWORD:zylp}
    reactive:
      url: ${DB_URL:postgresql://*************:35432/visthink_inventory}
      max-size: 20
      idle-timeout: PT10M

  # Hibernate配置
  hibernate-orm:
    database:
      generation: none
    log:
      sql: ${LOG_SQL:false}
      format-sql: true
      bind-parameters: true

  # Flyway数据库迁移
  flyway:
    migrate-at-start: true
    baseline-on-migrate: true
    locations: classpath:db/migration
    baseline-version: 1.0
    baseline-description: "Initial version"

  # 日志配置
  log:
    level: INFO
    category:
      "com.visthink": DEBUG
      "org.hibernate.SQL": ${LOG_SQL:false}
      "org.hibernate.type.descriptor.sql.BasicBinder": ${LOG_SQL:false}
    console:
      enable: true
      format: "%d{yyyy-MM-dd HH:mm:ss,SSS} %-5p [%c{3.}] (%t) %s%e%n"
      color: true

  # OpenAPI文档配置
  smallrye-openapi:
    info-title: 库存服务API
    info-version: 1.0.0
    info-description: Visthink ERP库存管理微服务API文档
    info-contact-name: Visthink Team
    info-contact-email: <EMAIL>
    servers:
      - url: http://localhost:8083
        description: 开发环境
      - url: http://localhost/api/inventory
        description: 网关环境

  # 健康检查配置
  smallrye-health:
    root-path: /health

  # 指标配置
  micrometer:
    binder:
      jvm: true
      system: true
      http-server: true
    export:
      prometheus:
        enabled: true
        path: /metrics

  # Redis配置
  redis:
    hosts: ${REDIS_HOSTS:redis://localhost:6379}
    password: ${REDIS_PASSWORD:}
    database: 2
    timeout: 10s
    max-pool-size: 20
    max-pool-waiting: 30

# 业务配置
app:
  # 租户配置
  tenant:
    enabled: true
    header-name: X-Tenant-Id
    default-tenant: 1

  # 缓存配置
  cache:
    inventory:
      ttl: 60 # 1分钟，库存数据实时性要求高
      max-size: 5000
    stock-alert:
      ttl: 300 # 5分钟
      max-size: 1000

  # 分页配置
  page:
    default-size: 20
    max-size: 100

  # 库存配置
  inventory:
    # 库存预警阈值
    warning-threshold: 10
    # 库存安全库存
    safety-stock: 5
    # 自动补货阈值
    reorder-point: 20
    # 库存盘点周期（天）
    stocktaking-cycle: 30
    # 库存变动日志保留天数
    log-retention-days: 90

  # 商品服务集成
  product-service:
    base-url: ${PRODUCT_SERVICE_URL:http://localhost:8082}
    timeout: 30s
    retry-attempts: 3

# 环境特定配置
"%dev":
  quarkus:
    log:
      category:
        "com.visthink": DEBUG
        "org.hibernate.SQL": true
        "org.hibernate.type.descriptor.sql.BasicBinder": true
    datasource:
      reactive:
        url: postgresql://localhost:35432/visthink_inventory
    redis:
      hosts: redis://localhost:6379

"%test":
  quarkus:
    datasource:
      db-kind: h2
      username: sa
      password:
      reactive:
        url: h2:mem:test;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    hibernate-orm:
      database:
        generation: drop-and-create
    flyway:
      migrate-at-start: false

"%prod":
  quarkus:
    log:
      level: INFO
      category:
        "com.visthink": INFO
        "org.hibernate.SQL": false
    datasource:
      reactive:
        url: ${DB_URL}
    redis:
      hosts: ${REDIS_HOSTS}

# 服务注册配置
consul:
  service:
    name: inventory-service
    port: 8083
    health-check-path: /health/ready
    health-check-interval: 10s
    tags:
      - microservice
      - inventory
      - quarkus
